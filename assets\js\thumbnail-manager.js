// Thumbnail Manager - Integrates with the main application
class ThumbnailManager {
    constructor(app) {
        this.app = app;
        this.generator = new ComprehensiveThumbnailGenerator();
        this.isGenerating = false;
        this.generationProgress = 0;
        
        this.init();
    }
    
    init() {
        // Auto-generate thumbnails when app is ready
        if (this.app.videos && this.app.videos.length > 0) {
            this.autoGenerateThumbnails();
        } else {
            // Wait for videos to load
            setTimeout(() => this.init(), 1000);
        }
    }
    
    // Automatically generate thumbnails for videos with placeholders
    async autoGenerateThumbnails() {
        if (this.isGenerating) return;
        
        console.log('ThumbnailManager: Starting automatic thumbnail generation');
        
        // Find videos that need thumbnails
        const videosNeedingThumbnails = this.app.videos.filter(video => 
            video.type !== 'photo' && 
            (video.thumbnail === '/file.svg' || !video.thumbnail)
        );
        
        if (videosNeedingThumbnails.length === 0) {
            console.log('ThumbnailManager: All videos already have thumbnails');
            return;
        }
        
        console.log(`ThumbnailManager: Found ${videosNeedingThumbnails.length} videos needing thumbnails`);
        
        this.isGenerating = true;
        this.showProgressIndicator();
        
        try {
            // Generate thumbnails in batches to avoid overwhelming the browser
            const batchSize = 5;
            const batches = this.createBatches(videosNeedingThumbnails, batchSize);
            
            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                console.log(`Processing batch ${i + 1}/${batches.length}`);
                
                // Process batch
                const batchPromises = batch.map(video => 
                    this.generator.generateThumbnailForVideo(video)
                );
                
                const batchResults = await Promise.allSettled(batchPromises);
                
                // Update video cards for successful generations
                batchResults.forEach((result, index) => {
                    if (result.status === 'fulfilled' && result.value.success) {
                        const video = batch[index];
                        this.updateVideoThumbnail(video, result.value.thumbnailUrl);
                    }
                });
                
                // Update progress
                this.generationProgress = ((i + 1) / batches.length) * 100;
                this.updateProgressIndicator();
                
                // Small delay between batches
                if (i < batches.length - 1) {
                    await this.delay(500);
                }
            }
            
            console.log('ThumbnailManager: Thumbnail generation completed');
            this.hideProgressIndicator();
            
        } catch (error) {
            console.error('ThumbnailManager: Error during thumbnail generation:', error);
            this.hideProgressIndicator();
        } finally {
            this.isGenerating = false;
        }
    }
    
    // Update video thumbnail in the data and UI
    updateVideoThumbnail(video, thumbnailUrl) {
        // Update video data
        video.thumbnail = thumbnailUrl;
        
        // Update UI
        this.generator.updateVideoCardThumbnail(video.id, thumbnailUrl);
        
        console.log(`Updated thumbnail for: ${video.title}`);
    }
    
    // Create batches from array
    createBatches(array, batchSize) {
        const batches = [];
        for (let i = 0; i < array.length; i += batchSize) {
            batches.push(array.slice(i, i + batchSize));
        }
        return batches;
    }
    
    // Show progress indicator
    showProgressIndicator() {
        // Remove existing indicator
        this.hideProgressIndicator();
        
        const indicator = document.createElement('div');
        indicator.id = 'thumbnailProgress';
        indicator.className = 'thumbnail-progress-indicator';
        indicator.innerHTML = `
            <div class="progress-content">
                <div class="progress-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                </div>
                <div class="progress-text">
                    <div class="progress-title">Generating Thumbnails</div>
                    <div class="progress-subtitle">Creating video previews...</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-percentage">0%</div>
            </div>
        `;
        
        document.body.appendChild(indicator);
        
        // Add styles if not already present
        if (!document.getElementById('thumbnailProgressStyles')) {
            const styles = document.createElement('style');
            styles.id = 'thumbnailProgressStyles';
            styles.textContent = `
                .thumbnail-progress-indicator {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    z-index: 10000;
                    min-width: 300px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    backdrop-filter: blur(10px);
                }
                
                .progress-content {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }
                
                .progress-icon {
                    color: #ff6b6b;
                    flex-shrink: 0;
                }
                
                .progress-text {
                    flex: 1;
                }
                
                .progress-title {
                    font-weight: 600;
                    font-size: 14px;
                    margin-bottom: 2px;
                }
                
                .progress-subtitle {
                    font-size: 12px;
                    color: #b3b3b3;
                    margin-bottom: 8px;
                }
                
                .progress-bar {
                    height: 4px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 2px;
                    overflow: hidden;
                }
                
                .progress-fill {
                    height: 100%;
                    background: #ff6b6b;
                    transition: width 0.3s ease;
                }
                
                .progress-percentage {
                    font-size: 12px;
                    color: #b3b3b3;
                    margin-left: 8px;
                    min-width: 35px;
                    text-align: right;
                }
            `;
            document.head.appendChild(styles);
        }
    }
    
    // Update progress indicator
    updateProgressIndicator() {
        const indicator = document.getElementById('thumbnailProgress');
        if (indicator) {
            const progressFill = indicator.querySelector('.progress-fill');
            const progressPercentage = indicator.querySelector('.progress-percentage');
            
            if (progressFill) {
                progressFill.style.width = `${this.generationProgress}%`;
            }
            
            if (progressPercentage) {
                progressPercentage.textContent = `${Math.round(this.generationProgress)}%`;
            }
        }
    }
    
    // Hide progress indicator
    hideProgressIndicator() {
        const indicator = document.getElementById('thumbnailProgress');
        if (indicator) {
            indicator.remove();
        }
    }
    
    // Manual thumbnail generation for specific video
    async generateThumbnailForVideo(videoId) {
        const video = this.app.videos.find(v => v.id === videoId);
        if (!video) {
            throw new Error('Video not found');
        }
        
        const result = await this.generator.generateThumbnailForVideo(video);
        
        if (result.success) {
            this.updateVideoThumbnail(video, result.thumbnailUrl);
        }
        
        return result;
    }
    
    // Regenerate all thumbnails
    async regenerateAllThumbnails() {
        if (this.isGenerating) {
            console.log('Thumbnail generation already in progress');
            return;
        }
        
        // Clear cache
        this.generator.cleanup();
        
        // Reset all video thumbnails to trigger regeneration
        this.app.videos.forEach(video => {
            if (video.type !== 'photo') {
                video.thumbnail = '/file.svg';
            }
        });
        
        // Start generation
        await this.autoGenerateThumbnails();
    }
    
    // Get generation statistics
    getStats() {
        const videosWithThumbnails = this.app.videos.filter(video => 
            video.type !== 'photo' && 
            video.thumbnail && 
            video.thumbnail !== '/file.svg'
        ).length;
        
        const totalVideos = this.app.videos.filter(video => video.type !== 'photo').length;
        
        return {
            totalVideos,
            videosWithThumbnails,
            videosNeedingThumbnails: totalVideos - videosWithThumbnails,
            isGenerating: this.isGenerating,
            progress: this.generationProgress,
            generatorStats: this.generator.getStats()
        };
    }
    
    // Utility method
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Cleanup
    destroy() {
        this.hideProgressIndicator();
        this.generator.cleanup();
    }
}

// Export for use
window.ThumbnailManager = ThumbnailManager;
