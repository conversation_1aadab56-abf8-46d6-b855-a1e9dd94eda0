// Thumbnail Generator Utility
class ThumbnailGenerator {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.canvas.width = 300;
        this.canvas.height = 200;
    }

    // Generate thumbnail from video file
    async generateVideoThumbnail(videoUrl, timeOffset = 5) {
        return new Promise((resolve, reject) => {
            const video = document.createElement('video');
            video.crossOrigin = 'anonymous';
            video.muted = true;
            
            video.addEventListener('loadedmetadata', () => {
                // Set time to capture frame (5 seconds or 10% of video duration)
                const captureTime = Math.min(timeOffset, video.duration * 0.1);
                video.currentTime = captureTime;
            });

            video.addEventListener('seeked', () => {
                try {
                    // Draw video frame to canvas
                    this.ctx.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
                    
                    // Convert canvas to blob
                    this.canvas.toBlob((blob) => {
                        if (blob) {
                            const thumbnailUrl = URL.createObjectURL(blob);
                            resolve(thumbnailUrl);
                        } else {
                            reject(new Error('Failed to generate thumbnail blob'));
                        }
                    }, 'image/jpeg', 0.8);
                } catch (error) {
                    reject(error);
                }
            });

            video.addEventListener('error', (error) => {
                reject(error);
            });

            video.src = videoUrl;
        });
    }

    // Generate placeholder thumbnail with video info
    generatePlaceholderThumbnail(video) {
        // Clear canvas
        this.ctx.fillStyle = '#2a2a2a';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Add gradient background
        const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height);
        gradient.addColorStop(0, '#2a2a2a');
        gradient.addColorStop(1, '#1a1a1a');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Add play button
        this.ctx.fillStyle = '#ff6b6b';
        this.ctx.beginPath();
        this.ctx.arc(this.canvas.width / 2, this.canvas.height / 2, 30, 0, 2 * Math.PI);
        this.ctx.fill();

        // Add play triangle
        this.ctx.fillStyle = 'white';
        this.ctx.beginPath();
        this.ctx.moveTo(this.canvas.width / 2 - 10, this.canvas.height / 2 - 15);
        this.ctx.lineTo(this.canvas.width / 2 - 10, this.canvas.height / 2 + 15);
        this.ctx.lineTo(this.canvas.width / 2 + 15, this.canvas.height / 2);
        this.ctx.closePath();
        this.ctx.fill();

        // Add video title (truncated)
        this.ctx.fillStyle = '#b3b3b3';
        this.ctx.font = '12px Arial, sans-serif';
        this.ctx.textAlign = 'center';
        const title = video.title.length > 30 ? video.title.substring(0, 30) + '...' : video.title;
        this.ctx.fillText(title, this.canvas.width / 2, this.canvas.height - 20);

        // Add duration
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 10px Arial, sans-serif';
        this.ctx.textAlign = 'right';
        this.ctx.fillText(video.duration, this.canvas.width - 10, 20);

        // Convert to blob URL
        return new Promise((resolve) => {
            this.canvas.toBlob((blob) => {
                const thumbnailUrl = URL.createObjectURL(blob);
                resolve(thumbnailUrl);
            }, 'image/jpeg', 0.8);
        });
    }

    // Cache generated thumbnails
    static thumbnailCache = new Map();

    static async getCachedThumbnail(videoId, generator) {
        if (this.thumbnailCache.has(videoId)) {
            return this.thumbnailCache.get(videoId);
        }

        const thumbnail = await generator();
        this.thumbnailCache.set(videoId, thumbnail);
        return thumbnail;
    }

    // Clean up blob URLs to prevent memory leaks
    static cleanupThumbnails() {
        this.thumbnailCache.forEach((url) => {
            if (url.startsWith('blob:')) {
                URL.revokeObjectURL(url);
            }
        });
        this.thumbnailCache.clear();
    }
}

// Export for use in other modules
window.ThumbnailGenerator = ThumbnailGenerator;
